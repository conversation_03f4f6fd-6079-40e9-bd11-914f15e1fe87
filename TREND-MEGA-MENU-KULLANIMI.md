# Trend Mega Menu Kullanım Kılavuzu

## <PERSON><PERSON> Ba<PERSON>ış

`trend-mega-menu` sınıfı, mevcut `has-mega-menu` sınıfı ile aynı işlevselliğe sahip yeni bir mega menü sınıfıdır. <PERSON><PERSON>ın<PERSON><PERSON>, ana menü öğelerine uygulandığında, o menü öğesi için mega menü özelliklerini etkinleştirir.

## Özellikler

- **Bloksuz Mega Menü**: Trendyol tarzı düz liste formatında 4 sütunlu yapı
- **Bloklu Mega Menü**: Sayfa içeriği + alt menüler kombinasyonu
- **Responsive Tasarım**: Mobil ve tablet uyumlu
- **Tüm Kategoriler Görünür**: Hover ile açılma yok, tüm kategoriler baştan görünür
- **Otomatik Sütun Dağılımı**: Her sütun maksimum 20 satır, otomatik geçiş

## Kurulum

### 1. Dosyalar Otomatik Olarak Dahil Edilmiştir

Aşağıdaki dosyalar tema klasörünüze eklenmiştir:

- `assets/js/trend-mega-menu.js` - JavaScript işlevsellik
- CSS stilleri `style.css` dosyasına eklenmiştir
- `functions.php` dosyası güncellenmiştir

### 2. WordPress Admin Paneli Ayarları

1. **WordPress Admin Paneli** → **Görünüm** → **Menüler**'e gidin
2. Mega menü yapmak istediğiniz ana menü öğesini seçin
3. **Ekran Seçenekleri** (sağ üst köşe) → **CSS Sınıfları**'nı işaretleyin
4. Menü öğesini genişletin ve **CSS Sınıfları** alanına `trend-mega-menu` yazın
5. Menüyü kaydedin

## Kullanım Türleri

### 1. Bloksuz Mega Menü (Hibrit Yapı)

**Nasıl Yapılır:**
- Ana menü öğesine `trend-mega-menu` sınıfını ekleyin
- Alt menüler oluşturun (çok seviyeli olabilir)
- Sayfa seçimi yapmayın veya boş bırakın

**Sonuç:**
- **1. Sütun**: Sadece ana kategoriler (1. seviye alt menüler)
- **2-3-4. Sütunlar**: Hover ile seçilen ana kategorinin tüm alt menüleri Trendyol tarzında düz liste
- Ana kategoriler kalın font, alt kategoriler normal font
- Her sütun maksimum 10 menü öğesi, otomatik geçiş
- Dikey sıralama

### 2. Bloklu Mega Menü (Sayfa İçeriği + Alt Menüler)

**Nasıl Yapılır:**
- Ana menü öğesine `trend-mega-menu` sınıfını ekleyin
- Alt menüler oluşturun
- Menü öğesi ayarlarından bir sayfa seçin

**Sonuç:**
- Seçilen sayfanın içeriği gösterilir
- Alt menüler sayfa içeriğinin altında listelenir

### 3. Sadece Sayfa İçeriği (Alt Menü Yok)

**Nasıl Yapılır:**
- Ana menü öğesine `trend-mega-menu` sınıfını ekleyin
- Alt menü oluşturmayın
- Menü öğesi ayarlarından bir sayfa seçin

**Sonuç:**
- Sadece seçilen sayfanın içeriği gösterilir

## CSS Sınıfları ve Yapısı

### Ana Sınıflar

```css
.trend-mega-menu                    /* Ana menü öğesi */
.trend-mega-menu-active             /* Aktif (açık) mega menü */
.mega-menu-column-1                 /* 1. sütun (ana kategoriler) */
.mega-menu-column-2                 /* 2. sütun (alt kategoriler) */
.mega-menu-column-3                 /* 3. sütun (alt-alt kategoriler) */
.mega-menu-column-4                 /* 4. sütun (son seviye) */
```

### Responsive Breakpoint'ler

- **Desktop**: 4 sütunlu grid (1024px+)
- **Tablet**: 2 sütunlu grid (768px - 1024px)
- **Mobile**: 1 sütunlu grid (768px-)

## JavaScript İşlevsellik

### Otomatik Özellikler

- **Hover Delay**: 150ms gecikme ile menü kapanır
- **Trendyol Style Conversion**: Alt menüler otomatik olarak düz liste formatına dönüştürülür
- **Static Display**: Tüm kategoriler baştan görünür, dinamik yükleme yok
- **Auto Column Distribution**: Öğeler otomatik olarak sütunlara dağıtılır (10 menü öğesi/sütun)
- **Mobile Optimization**: Mobilde tüm sütunlar görünür

### Event Listeners

- `mouseenter/mouseleave` - Menü açma/kapama
- `click` - Dış tıklama ile kapanma
- `keydown` - ESC tuşu ile kapanma
- `resize` - Pencere boyutu değişikliği (responsive)

## Örnekler

### Örnek 1: E-ticaret Kategorileri

```
Ana Kategori: Elektronik (trend-mega-menu)
├── Alt Kategori 1: Telefon
│   ├── iPhone
│   ├── Samsung
│   └── Xiaomi
├── Alt Kategori 2: Bilgisayar
│   ├── Laptop
│   ├── Desktop
│   └── Tablet
└── Alt Kategori 3: Aksesuar
    ├── Kılıf
    ├── Şarj Aleti
    └── Kulaklık
```

### Örnek 2: Hizmetler Mega Menüsü

```
Ana Kategori: Hizmetlerimiz (trend-mega-menu + sayfa seçimi)
├── Sayfa İçeriği: Hizmetler sayfasının içeriği
└── Alt Menüler:
    ├── Web Tasarım
    ├── SEO
    └── Sosyal Medya
```

## Yeni Hibrit Yapı Davranışı

#### **Önceki Davranış:**
```
Ana Kategori (hover) → Alt Kategori (hover) → Alt-Alt Kategori (hover) → Son Seviye
```

#### **Yeni Davranış (Hibrit Yapı):**
```
Sütun 1              Sütun 2 (Hover)     Sütun 3 (Hover)     Sütun 4 (Hover)
--------             ---------------     ---------------     ---------------
Ana Kategori 1  →    Alt Kategori 1      Alt Kategori 11     Alt Kategori 21
Ana Kategori 2       Alt Kategori 2      Alt Kategori 12     Alt Kategori 22
Ana Kategori 3       Alt Kategori 3      Alt Kategori 13     Alt Kategori 23
Ana Kategori 4       Alt Kategori 4      Alt Kategori 14     Alt Kategori 24
Ana Kategori 5       Alt Kategori 5      Alt Kategori 15     Alt Kategori 25
...                  Alt Kategori 6      Alt Kategori 16     Alt Kategori 26
                     Alt Kategori 7      Alt Kategori 17     Alt Kategori 27
                     Alt Kategori 8      Alt Kategori 18     Alt Kategori 28
                     Alt Kategori 9      Alt Kategori 19     Alt Kategori 29
                     Alt Kategori 10     Alt Kategori 20     Alt Kategori 30
                     (max 10 öğe)        (max 10 öğe)        (max 10 öğe)
```

**Açıklama:**
- 1. sütunda sadece ana kategoriler görünür
- Ana kategoriye hover yapıldığında, o kategorinin TÜM alt menüleri 2-3-4. sütunlarda düz liste halinde gösterilir
- Alt menüler hiyerarşik değil, düz liste formatında
- Her sütun maksimum 10 menü öğesi, fazlası bir sonraki sütuna geçer

## Sorun Giderme

### Mega Menü Görünmüyor

1. CSS sınıfının doğru yazıldığını kontrol edin: `trend-mega-menu`
2. JavaScript dosyasının yüklendiğini kontrol edin
3. Tarayıcı konsolunda hata olup olmadığını kontrol edin

### Sütunlar Düzgün Görünmüyor

1. Alt menülerin doğru şekilde oluşturulduğunu kontrol edin
2. Responsive breakpoint'leri kontrol edin
3. CSS çakışması olup olmadığını kontrol edin

### Hover Efektleri Çalışmıyor

1. JavaScript dosyasının yüklendiğini kontrol edin
2. Mobil cihazda olup olmadığınızı kontrol edin (mobilde hover yok)
3. Tarayıcı konsolunda JavaScript hatası olup olmadığını kontrol edin

## Teknik Detaylar

### Dosya Yapısı

```
/assets/js/trend-mega-menu.js       - JavaScript işlevsellik
/style.css                          - CSS stilleri (eklendi)
/functions.php                      - Walker sınıfı (güncellendi)
```

### Browser Desteği

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Internet Explorer 11 (sınırlı destek)

### Performance

- Lazy loading alt kategoriler
- Debounced hover events
- Optimized CSS transitions
- Mobile-first responsive design

## Güncelleme Notları

Bu mega menü sistemi, mevcut `has-mega-menu` sisteminden bağımsız çalışır ve aynı sayfada her iki sistem de kullanılabilir.
