/**
 * User Avatar Dropdown JavaScript
 * Gelismis kullanici avatar dropdown islevsellik - Hover efekti ile
 */

// Sayfa yuklendiginde calistir
document.addEventListener('DOMContentLoaded', function() {
    const avatarButton = document.getElementById('user-avatar-toggle');
    const dropdownMenu = document.getElementById('user-dropdown-menu');
    const userProfileDropdown = document.querySelector('.user-profile-dropdown');

    if (!avatarButton || !dropdownMenu || !userProfileDropdown) {
        return; // Elementler bulunamazsa cik
    }

    let hoverTimeout;

    // Mouse avatar uzerine geldiginde dropdown'u ac
    userProfileDropdown.addEventListener('mouseenter', function() {
        clearTimeout(hoverTimeout);
        openDropdown();
    });

    // Mouse avatar'dan ayrildiginda dropdown'u kapat (gecikme ile)
    userProfileDropdown.addEventListener('mouseleave', function() {
        hoverTimeout = setTimeout(function() {
            closeDropdown();
        }, 150); // 150ms gecikme
    });

    // Dropdown uzerine mouse geldiginde kapatmayi iptal et
    dropdownMenu.addEventListener('mouseenter', function() {
        clearTimeout(hoverTimeout);
    });

    // Dropdown'dan mouse ayrildiginda kapat
    dropdownMenu.addEventListener('mouseleave', function() {
        hoverTimeout = setTimeout(function() {
            closeDropdown();
        }, 150); // 150ms gecikme
    });

    // ESC tusuna basildiginda kapat
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeDropdown();
        }
    });

    // Dropdown menu itemlarina klavye navigasyonu
    dropdownMenu.addEventListener('keydown', function(e) {
        const menuItems = dropdownMenu.querySelectorAll('.user-menu-item');
        const currentIndex = Array.from(menuItems).findIndex(item => item === document.activeElement);

        switch(e.key) {
            case 'ArrowDown':
                e.preventDefault();
                const nextIndex = currentIndex < menuItems.length - 1 ? currentIndex + 1 : 0;
                menuItems[nextIndex].focus();
                break;
            case 'ArrowUp':
                e.preventDefault();
                const prevIndex = currentIndex > 0 ? currentIndex - 1 : menuItems.length - 1;
                menuItems[prevIndex].focus();
                break;
            case 'Enter':
            case ' ':
                e.preventDefault();
                if (document.activeElement.classList.contains('user-menu-item')) {
                    document.activeElement.click();
                }
                break;
        }
    });

    // Dropdown'u ac
    function openDropdown() {
        dropdownMenu.style.display = 'block';
        // Animasyon icin kisa gecikme
        setTimeout(function() {
            dropdownMenu.classList.add('active');
        }, 10);
        avatarButton.setAttribute('aria-expanded', 'true');
    }

    // Dropdown'u kapat
    function closeDropdown() {
        dropdownMenu.classList.remove('active');
        // Animasyon tamamlandiktan sonra gizle
        setTimeout(function() {
            if (!dropdownMenu.classList.contains('active')) {
                dropdownMenu.style.display = 'none';
            }
        }, 150);
        avatarButton.setAttribute('aria-expanded', 'false');
    }


});
