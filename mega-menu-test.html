<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mega Menu Test</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-info {
            background: #f0f0f0;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .test-info h2 {
            margin-top: 0;
            color: #333;
        }
        .test-info ul {
            margin: 10px 0;
        }
        .test-info li {
            margin: 5px 0;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .status.fixed {
            background: #d4edda;
            color: #155724;
        }
        .status.issue {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-info">
            <h2>Mega Menu Test - Hover Davranı<PERSON> ve <PERSON> Si<PERSON></h2>
            <p><strong>Test <PERSON><PERSON><PERSON>:</strong></p>

            <h3>Yapılan Düzeltmeler:</h3>
            <ul>
                <li><span class="status fixed">✅ Düzeltildi</span> 2. sütun: Alt menüsü olmayan öğelerin üzerine gelindiğinde 3. sütunun gizlenmesi</li>
                <li><span class="status fixed">✅ Düzeltildi</span> 3. sütun: Alt menüsü olmayan öğelerin üzerine gelindiğinde 4. sütunun gizlenmesi</li>
                <li><span class="status fixed">✅ Zaten Çalışıyor</span> 1. sütun: Alt menüsü olmayan öğelerin üzerine gelindiğinde tüm sağ sütunların gizlenmesi</li>
                <li><span class="status fixed">✅ Yeni Eklendi</span> Mega menü öğelerinin yanında aşağı bakan ok simgesi</li>
            </ul>

            <h3>Test Senaryoları:</h3>
            <ol>
                <li><strong>Ok Simgesi:</strong> "Test Mega Menü" yanında ▼ simgesi görünmeli</li>
                <li><strong>Ok Animasyonu:</strong> Mega menü açıldığında ok 180° dönerek ▲ olmalı</li>
                <li><strong>Ok Rengi:</strong> Hover ve aktif durumda ok turuncu (#ff6000) olmalı</li>
                <li>1. sütundaki alt menüsü olan bir öğenin üzerine gelin → 2. sütun görünmeli</li>
                <li>2. sütundaki alt menüsü olan bir öğenin üzerine gelin → 3. sütun görünmeli</li>
                <li>2. sütundaki alt menüsü olmayan bir öğenin üzerine gelin → 3. sütun gizlenmeli</li>
                <li>3. sütundaki alt menüsü olan bir öğenin üzerine gelin → 4. sütun görünmeli</li>
                <li>3. sütundaki alt menüsü olmayan bir öğenin üzerine gelin → 4. sütun gizlenmeli</li>
                <li>1. sütundaki alt menüsü olmayan bir öğenin üzerine gelin → Tüm sağ sütunlar gizlenmeli</li>
            </ol>
        </div>

        <!-- Simulated Header Structure -->
        <header class="site-header">
            <div class="header-bottom">
                <div class="container">
                    <nav class="main-navigation">
                        <ul id="primary-menu">
                            <!-- Normal Menu Item -->
                            <li><a href="#">Normal Menü</a></li>
                            
                            <!-- Mega Menu Item with Submenu -->
                            <li class="has-mega-menu">
                                <a href="#">Test Mega Menü</a>
                                <ul class="sub-menu">
                                    <div class="sub-menu-container">
                                        <!-- 1. Sütun - Ana kategoriler -->
                                        <div class="mega-menu-column-1">
                                            <ul>
                                                <li><a href="#" class="has-submenu">Alt Menüsü Olan Kategori 1</a>
                                                    <ul>
                                                        <li><a href="#">Alt Kategori 1.1</a></li>
                                                        <li><a href="#" class="has-submenu">Alt Kategori 1.2 (Alt menüsü var)</a>
                                                            <ul>
                                                                <li><a href="#">Derin Alt Kategori 1.2.1</a></li>
                                                                <li><a href="#">Derin Alt Kategori 1.2.2</a></li>
                                                            </ul>
                                                        </li>
                                                        <li><a href="#">Alt Kategori 1.3 (Alt menüsü yok)</a></li>
                                                    </ul>
                                                </li>
                                                <li><a href="#" class="has-submenu">Alt Menüsü Olan Kategori 2</a>
                                                    <ul>
                                                        <li><a href="#">Alt Kategori 2.1</a></li>
                                                        <li><a href="#">Alt Kategori 2.2</a></li>
                                                    </ul>
                                                </li>
                                                <li><a href="#">Alt Menüsü Olmayan Kategori</a></li>
                                            </ul>
                                        </div>
                                        
                                        <!-- 2. Sütun - Alt kategoriler (JavaScript ile doldurulacak) -->
                                        <div class="mega-menu-column-2"></div>
                                        
                                        <!-- 3. Sütun - Daha derin alt kategoriler (JavaScript ile doldurulacak) -->
                                        <div class="mega-menu-column-3"></div>
                                        
                                        <!-- 4. Sütun - En derin alt kategoriler (JavaScript ile doldurulacak) -->
                                        <div class="mega-menu-column-4"></div>
                                    </div>
                                </ul>
                            </li>
                            
                            <!-- Another Normal Menu Item -->
                            <li><a href="#">Başka Menü</a></li>
                        </ul>
                    </nav>
                </div>
            </div>
        </header>

        <div style="height: 500px; background: #f9f9f9; margin-top: 20px; padding: 20px;">
            <h3>Test Alanı</h3>
            <p>Yukarıdaki "Test Mega Menü" öğesinin üzerine gelin ve alt menü davranışlarını test edin.</p>
            <p>Konsolu açarak debug mesajlarını görebilirsiniz.</p>
        </div>
    </div>

    <script src="assets/js/mega-menu.js"></script>
</body>
</html>
