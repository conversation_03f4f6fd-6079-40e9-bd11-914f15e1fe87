/**
 * Trend Mega Menu JavaScript for DmrThema
 * Fare ile trend mega menu uzerinde gezinirken menunun kapanmasini onler
 */

document.addEventListener('DOMContentLoaded', function() {

    // Trend mega menu elementlerini sec
    const trendMegaMenuItems = document.querySelectorAll('.main-navigation ul li.trend-mega-menu');

    if (trendMegaMenuItems.length === 0) {
        return; // Trend mega menu yoksa cik
    }
    
    // Her trend mega menu item icin event listener'lar ekle
    trendMegaMenuItems.forEach(function(menuItem) {
        const subMenu = menuItem.querySelector('.sub-menu');

        if (!subMenu) {
            return; // Alt menu yoksa devam et
        }

        let hoverTimeout;

        // Menu item uzerine fare geldiginde
        menuItem.addEventListener('mouseenter', function() {
            // Timeout'u temizle (eger varsa)
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }

            // Diger acik trend mega menuleri kapat
            closeTrendMegaMenus();

            // Bu trend mega menuyu ac - animasyon icin kisa gecikme
            requestAnimationFrame(function() {
                menuItem.classList.add('trend-mega-menu-active');
                // 4 sütunlu trend mega menu için hover efektlerini başlat
                initializeTrendMegaMenuHover(menuItem);
            });
        });
        
        // Menu item'dan fare ciktiginda
        menuItem.addEventListener('mouseleave', function() {
            // Kisa bir gecikme ile menu kapat
            // Bu gecikme fareyi mega menu uzerine tasima firsati verir
            hoverTimeout = setTimeout(function() {
                menuItem.classList.remove('trend-mega-menu-active');
            }, 150); // 150ms gecikme
        });
        
        // Sub menu uzerine fare geldiginde
        subMenu.addEventListener('mouseenter', function() {
            // Timeout'u temizle - menu acik kalsin
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }
            
            // Menu acik kalsin
            menuItem.classList.add('trend-mega-menu-active');
        });
        
        // Sub menu'dan fare ciktiginda
        subMenu.addEventListener('mouseleave', function() {
            // Menu kapat
            menuItem.classList.remove('trend-mega-menu-active');
        });
    });
    
    // Tum trend mega menuleri kapat
    function closeTrendMegaMenus() {
        trendMegaMenuItems.forEach(function(item) {
            item.classList.remove('trend-mega-menu-active');
        });
    }
    
    // Sayfa uzerinde baska bir yere tiklandiginda trend mega menuleri kapat
    document.addEventListener('click', function(e) {
        // Tiklanilan element trend mega menu icinde degilse
        let isInsideTrendMegaMenu = false;
        
        trendMegaMenuItems.forEach(function(menuItem) {
            if (menuItem.contains(e.target)) {
                isInsideTrendMegaMenu = true;
            }
        });
        
        if (!isInsideTrendMegaMenu) {
            closeTrendMegaMenus();
        }
    });
    
    // ESC tusuna basildiginda trend mega menuleri kapat
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeTrendMegaMenus();
        }
    });

    // 4 sütunlu trend mega menu için hibrit yapıyı başlat
    function initializeTrendMegaMenuHover(menuItem) {
        const subMenuContainer = menuItem.querySelector('.sub-menu-container');
        if (!subMenuContainer) return;

        // Bloklu mega menu kontrolü - sayfa içeriği varsa grid yapısını uygulama
        const hasPageContent = subMenuContainer.querySelector('.mega-menu-page-content');
        if (hasPageContent) {
            // Bloklu mega menu - hiçbir şey yapma, mevcut yapıyı koru
            return;
        }

        // Mobil kontrolü
        const isMobile = window.innerWidth <= 768;
        const isTablet = window.innerWidth <= 1024 && window.innerWidth > 768;

        // Sadece bloksuz mega menüler için hibrit yapıya dönüştür
        convertToHybridLayout(subMenuContainer);

        // Mobilde farklı davranış
        if (isMobile) {
            // Mobilde tüm sütunları göster, hover efekti yok
            showAllColumnsForMobile(subMenuContainer);
            return;
        }

        // İlk sütundaki ana menü öğelerine hover efekti ekle
        const column1 = subMenuContainer.querySelector('.mega-menu-column-1');
        if (!column1) return;

        const mainMenuItems = column1.querySelectorAll('li > a');

        mainMenuItems.forEach(function(mainItem, index) {
            // Hover efekti için debounce
            let hoverTimer;

            mainItem.addEventListener('mouseenter', function() {
                // Mobil kontrolü tekrar yap
                if (window.innerWidth <= 768) return;

                // Önceki timer'ı temizle
                if (hoverTimer) {
                    clearTimeout(hoverTimer);
                }

                // Kısa gecikme ile hover efektini uygula
                hoverTimer = setTimeout(() => {
                    // Tüm ana menü öğelerinden active sınıfını kaldır
                    mainMenuItems.forEach(item => item.classList.remove('active'));

                    // Bu öğeye active sınıfı ekle
                    this.classList.add('active');

                    // 2-4. sütunları gizle
                    hideAllSubColumns(subMenuContainer);

                    // Alt kategorileri Trendyol tarzında göster
                    showSubCategoriesTrendyolStyle(subMenuContainer, this, index, isTablet);
                }, 100); // 100ms gecikme
            });

            mainItem.addEventListener('mouseleave', function() {
                if (hoverTimer) {
                    clearTimeout(hoverTimer);
                }
            });
        });
    }

    // Hibrit yapıya dönüştürme fonksiyonu (1. sütun sadece ana kategoriler, 2-4. sütun hover ile alt kategoriler)
    function convertToHybridLayout(container) {
        // Mevcut li elementlerini al
        const listItems = Array.from(container.children).filter(child => child.tagName === 'LI');

        if (listItems.length === 0) return;

        // Sütun div'lerini oluştur
        const column1 = document.createElement('div');
        column1.className = 'mega-menu-column-1';

        const column2 = document.createElement('div');
        column2.className = 'mega-menu-column-2';

        const column3 = document.createElement('div');
        column3.className = 'mega-menu-column-3';

        const column4 = document.createElement('div');
        column4.className = 'mega-menu-column-4';

        // İlk sütuna sadece ana menü öğelerini ekle (1. seviye)
        const mainList = document.createElement('ul');
        listItems.forEach(item => {
            const mainLink = item.querySelector('a');
            if (mainLink) {
                const li = document.createElement('li');
                const a = document.createElement('a');
                a.href = mainLink.href;
                a.textContent = mainLink.textContent.trim();
                a.setAttribute('data-menu-item', item.outerHTML); // Orijinal menü yapısını sakla

                // Alt menüye sahip olup olmadığını kontrol et
                const subMenu = item.querySelector('ul');
                if (subMenu && subMenu.children.length > 0) {
                    a.classList.add('has-submenu');
                }

                li.appendChild(a);
                mainList.appendChild(li);
            }
        });
        column1.appendChild(mainList);

        // Yeni sütunları oluştur
        const column5 = document.createElement('div');
        column5.className = 'mega-menu-column-5';

        const column6 = document.createElement('div');
        column6.className = 'mega-menu-column-6';

        const column7 = document.createElement('div');
        column7.className = 'mega-menu-column-7';

        // Container'ı temizle ve sütunları ekle
        container.innerHTML = '';
        container.appendChild(column1);
        container.appendChild(column2);
        container.appendChild(column3);
        container.appendChild(column4);
        container.appendChild(column5);
        container.appendChild(column6);
        container.appendChild(column7);
    }

    // Tüm alt sütunları gizle
    function hideAllSubColumns(container) {
        const columns = container.querySelectorAll('.mega-menu-column-2, .mega-menu-column-3, .mega-menu-column-4');
        columns.forEach(column => {
            column.classList.remove('active');
            column.innerHTML = '';
        });
    }

    // Mobil için tüm sütunları göster
    function showAllColumnsForMobile(container) {
        const columns = container.querySelectorAll('.mega-menu-column-2, .mega-menu-column-3, .mega-menu-column-4');
        columns.forEach(column => {
            column.classList.add('active');
        });
    }

    // Belirli bir öğe için alt kategorileri hiyerarşik yapıda göster
    function showSubCategoriesTrendyolStyle(container, clickedItem, itemIndex, isTablet) {
        // Orijinal menü yapısını al
        const originalMenuData = clickedItem.getAttribute('data-menu-item');
        if (!originalMenuData) return;

        // Geçici div oluştur ve HTML'i parse et
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = originalMenuData;
        const originalItem = tempDiv.firstChild;

        // Alt menüleri bul
        const subMenu = originalItem.querySelector('ul');
        if (!subMenu || subMenu.children.length === 0) return;

        // Hiyerarşik yapıyı koruyarak alt menü öğelerini topla
        const hierarchicalItems = [];

        function collectHierarchicalItems(menuElement, level = 1) {
            const items = menuElement.querySelectorAll(':scope > li');
            items.forEach(item => {
                const link = item.querySelector(':scope > a');
                if (link) {
                    // Ana kategori (1. seviye) ekle - Fizik, 1 gibi
                    hierarchicalItems.push({
                        text: link.textContent.trim(),
                        url: link.href,
                        level: level,
                        type: 'main-category' // Her zaman ana kategori olarak işaretle
                    });

                    // Bu ana kategorinin alt menülerini ekle (2. seviye) - Sepet, 2, 4, 3 gibi
                    const subUl = item.querySelector(':scope > ul');
                    if (subUl) {
                        const subItems = subUl.querySelectorAll(':scope > li');
                        subItems.forEach(subItem => {
                            const subLink = subItem.querySelector(':scope > a');
                            if (subLink) {
                                hierarchicalItems.push({
                                    text: subLink.textContent.trim(),
                                    url: subLink.href,
                                    level: level + 1,
                                    type: 'sub-category' // Alt kategori olarak işaretle
                                });
                            }
                        });
                    }
                }
            });
        }

        collectHierarchicalItems(subMenu);

        // Öğeleri sütunlara dağıt (her sütun maksimum 10 öğe)
        const itemsPerColumn = 10;
        const columns = [
            container.querySelector('.mega-menu-column-2'),
            container.querySelector('.mega-menu-column-3'),
            container.querySelector('.mega-menu-column-4'),
            container.querySelector('.mega-menu-column-5'),
            container.querySelector('.mega-menu-column-6'),
            container.querySelector('.mega-menu-column-7')
        ];

        hierarchicalItems.forEach((item, index) => {
            const columnIndex = Math.floor(index / itemsPerColumn);
            if (columnIndex < 6) { // Maksimum 6 sütun (2, 3, 4, 5, 6, 7)
                const column = columns[columnIndex];

                const menuItemDiv = document.createElement('div');
                menuItemDiv.className = `trend-menu-item ${item.type}`;

                const link = document.createElement('a');
                link.href = item.url;
                link.textContent = item.text;

                menuItemDiv.appendChild(link);
                column.appendChild(menuItemDiv);
                column.classList.add('active');
            }
        });
    }

});
