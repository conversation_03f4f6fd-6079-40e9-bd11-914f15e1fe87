/**
 * Education Panel JavaScript
 * Egitim paneli buton fonksiyonlari
 */

(function($) {
    'use strict';

    // DOM hazir oldugunda calistir
    $(document).ready(function() {
        initEducationPanel();
    });

    function initEducationPanel() {
        const educationButton = $('#education-panel-button');

        // Egitim paneli butonuna tiklandiginda
        educationButton.on('click', function(e) {
            e.preventDefault();
            
            // Tutor kontrol paneline yonlendir
            // WordPress admin URL'ini olustur
            const adminUrl = window.location.origin + '/wp-admin/';
            const tutorUrl = adminUrl + 'admin.php?page=tutor';
            
            // Yeni sekmede ac
            window.open(tutorUrl, '_blank');
        });

        // Hover efektleri icin ek JavaScript (opsiyonel)
        educationButton.on('mouseenter', function() {
            $(this).find('.education-panel-icon').css('transform', 'scale(1.05)');
        });

        educationButton.on('mouseleave', function() {
            $(this).find('.education-panel-icon').css('transform', 'scale(1)');
        });
    }

})(jQuery);
